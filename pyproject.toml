[tool.poetry]
name = "chat"
version = "0.1.0"
description = "Chat package for triple3-chat"
authors = [
    "Your Name <<EMAIL>>"
]
readme = "README.md"
packages = [
    {include = "chat", from = "src"},
    {include = "bible_study_mcp", from = "src"}
]

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
langchain = "^0.3.0"
langchain-openai = "^0.2.0"
langchain-anthropic = "^0.2.0"
langchain-google-genai = "^2.0.0"
langchain-community = "^0.3.0"
langgraph = "^0.2.74"
langchain-mcp-adapters = "^0.1.0"
fastapi = "^0.115.2"
uvicorn = "^0.27.0"
pydantic = "^2.6.0"
python-dotenv = "^1.0.0"
pydantic-settings = "^2.8.1"
python-docx = "^1.2.0"
mcp = "^1.10.1"
gradio = "^5.38.2"

[tool.poetry.group.dev.dependencies]
ruff = "^0.9.3"
nbqa = "^1.9.1"
pytest-asyncio = "^0.26.0"
pytest-cov = "^6.1.1"
pytest = "^8.4.1"
pre-commit = "^4.2.0"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 88
target-version = "py310"

[tool.ruff.lint]
select = ["E", "F", "I", "N", "W", "UP"]
ignore = []

[tool.ruff.format]
quote-style = "double"
indent-style = "space"

[tool.ruff.lint.isort]
known-first-party = ["chat", "bible_study_mcp"]

[tool.pytest.ini_options]
asyncio_mode = "strict"
asyncio_default_fixture_loop_scope = "function"
