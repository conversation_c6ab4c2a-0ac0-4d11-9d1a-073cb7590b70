#!/usr/bin/env python3
"""
Example script to run the Bible Study MCP server.

This script demonstrates how to start the MCP server for providing
Bible study context to AI chatbots using the 三三制 (Triple-Three Method).

Usage:
    python examples/run_mcp_server.py

The server will start and listen for MCP protocol messages on stdin/stdout.
"""

import logging
import os
import sys
from pathlib import Path

# Ensure we're running from the project root directory
script_dir = Path(__file__).parent
project_root = script_dir.parent

# Change to project root directory to ensure <PERSON> can find pyproject.toml
os.chdir(project_root)

# Add the src directory to the Python path
sys.path.insert(0, str(project_root / "src"))

from bible_study_mcp.server import main  # noqa: E402


def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler("mcp_server.log"),
            logging.StreamHandler(sys.stderr),
        ],
    )


if __name__ == "__main__":
    # Print debug info to stderr so it shows in Claude Desktop logs
    print(f"Script directory: {script_dir}", file=sys.stderr)
    print(f"Project root: {project_root}", file=sys.stderr)
    print(f"Current working directory: {os.getcwd()}", file=sys.stderr)
    print("", file=sys.stderr)

    print("Starting Bible Study MCP Server...", file=sys.stderr)
    print(
        "Server will provide context for 三三制 (Triple-Three Method) Bible study.",
        file=sys.stderr,
    )
    print("Press Ctrl+C to stop the server.", file=sys.stderr)
    print("", file=sys.stderr)

    setup_logging()

    try:
        main()
    except KeyboardInterrupt:
        print("\nServer stopped by user.", file=sys.stderr)
    except Exception as e:
        print(f"Server error: {e}", file=sys.stderr)
        sys.exit(1)
