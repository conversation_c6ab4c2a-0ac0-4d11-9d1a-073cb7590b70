#!/usr/bin/env python3
"""
Gradio-based chatbot using the local chat agent with MCP tools.

This script provides a web-based interface to interact with the
Bible study agent service locally without needing to run the full API server.
Uses the LangGraph agent with MCP tools for enhanced functionality.
"""

import asyncio
import sys
from collections.abc import Async<PERSON>enerator, Iterator
from pathlib import Path

import gradio as gr
from gradio import ChatMessage

# Add the src directory to Python path so we can import chat modules
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from chat.agent_service import AgentService  # noqa: E402
from chat.config import settings  # noqa: E402
from chat.models import ChatCompletionRequest, Message, Role  # noqa: E402


class GradioChatbot:
    """Gradio-based chatbot interface."""

    def __init__(self):
        """Initialize the chatbot."""
        self.current_model = settings.default_model
        self.streaming_enabled = True

        # Ensure we have an API key for OpenRouter
        if not settings.openrouter_api_key:
            print("⚠️  Warning: OPENROUTER_API_KEY not set in environment")
            print("   Set it in .env file or export OPENROUTER_API_KEY=your_key")

    def get_available_models(self) -> list[tuple[str, str]]:
        """Get list of available models for dropdown."""
        return [(f"{shorthand} → {full_id}", shorthand)
                for shorthand, full_id in settings.available_models.items()]

    def switch_model(self, new_model: str) -> str:
        """Switch to a different model."""
        if new_model in settings.available_models:
            self.current_model = new_model
            return f"✅ 已切换到模型: {new_model}"
        else:
            return f"❌ 未知模型: {new_model}"

    def toggle_streaming(self, streaming: bool) -> str:
        """Toggle streaming mode."""
        self.streaming_enabled = streaming
        return f"流式输出已{'开启' if streaming else '关闭'}"

    async def get_chat_response_async(
        self, messages: list[Message]
    ) -> AsyncGenerator[str, None]:
        """Get response from agent asynchronously."""
        request = ChatCompletionRequest(
            model=self.current_model,
            messages=messages,
            stream=self.streaming_enabled,
            temperature=settings.temperature,
            max_tokens=settings.max_tokens,
        )

        if self.streaming_enabled:
            full_response = ""
            try:
                async for chunk in AgentService.stream_chat_completion(request):
                    if chunk.choices and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        full_response += content
                        yield full_response
            except Exception as e:
                yield f"❌ 错误: {e}"
        else:
            try:
                response = await AgentService.generate_chat_completion(request)
                yield response.choices[0].message.content
            except Exception as e:
                yield f"❌ 错误: {e}"

    def chat_response(self, message: str, history: list[ChatMessage]) -> Iterator[str]:
        """Handle chat response with simplified async execution."""
        try:
            # Convert Gradio history to our Message format
            messages = []
            for msg in history:
                # Handle both dict and object formats for messages
                if isinstance(msg, dict):
                    role = Role.USER if msg["role"] == "user" else Role.ASSISTANT
                    content = self._extract_text_content(msg["content"])
                else:
                    role = Role.USER if msg.role == "user" else Role.ASSISTANT
                    content = self._extract_text_content(msg.content)
                messages.append(Message(role=role, content=content))

            # Add current user message
            messages.append(Message(role=Role.USER, content=message))

            # Use asyncio.run to handle the async call cleanly
            responses = asyncio.run(self._collect_async_responses(messages))
            yield from responses

        except Exception as e:
            yield f"❌ 错误: {e}"

    def _extract_text_content(self, content) -> str:
        """Extract text content from Gradio message content, handling various types."""
        if isinstance(content, str):
            return content
        elif isinstance(content, list | tuple):
            # Handle multimodal content - extract text parts
            text_parts = []
            for item in content:
                if isinstance(item, str):
                    text_parts.append(item)
                elif isinstance(item, dict):
                    if "text" in item:
                        text_parts.append(item["text"])
                    elif "content" in item:
                        text_parts.append(str(item["content"]))
            return " ".join(text_parts) if text_parts else "[非文本内容]"
        elif isinstance(content, dict):
            # Handle dict-based content
            if "text" in content:
                return content["text"]
            elif "content" in content:
                return str(content["content"])

        # Fallback: convert to string
        try:
            return str(content)
        except Exception:
            return "[无法解析的内容]"

    async def _collect_async_responses(self, messages: list[Message]) -> list[str]:
        """Collect all responses from async generator."""
        responses = []
        last_response = ""

        async for response in self.get_chat_response_async(messages):
            if response != last_response:  # Only yield if content changed
                responses.append(response)
                last_response = response

        # Return all responses for the iterator
        return responses if responses else ["[无响应]"]


def create_gradio_interface():
    """Create and configure the Gradio interface."""
    chatbot = GradioChatbot()

    # Custom CSS for better styling
    css = """
    .gradio-container {
        max-width: 1400px !important;
        margin: auto;
    }
    .chat-container {
        height: 700px;
    }
    .model-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        border-radius: 10px;
        margin: 10px 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 20px;
        text-align: center;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }
    .main-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 600;
    }
    .main-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }
    .control-panel {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        border: 1px solid #e9ecef;
    }
    .feature-list {
        background: #e3f2fd;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
    }
    .config-info {
        background: #f3e5f5;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
    }
    .chat-interface {
        border-radius: 15px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    """

    with gr.Blocks(css=css, title="三三制聊天 - 查经助手") as demo:
        gr.HTML("""
        <div class="main-header">
            <h1>🤖 三三制聊天 - 查经助手</h1>
            <p>基于 MCP 工具的交互式查经方法论网页界面</p>
            <p style="font-size: 0.9rem; margin-top: 15px;">
                💡 支持智能问答、文档查询、实践指导
            </p>
        </div>
        """)

        with gr.Row():
            with gr.Column(scale=3):
                # Main chat interface
                with gr.Column(elem_classes=["chat-interface"]):
                    gr.ChatInterface(
                        fn=chatbot.chat_response,
                        type="messages",
                        chatbot=gr.Chatbot(
                            height=650,
                            show_label=False,
                            avatar_images=(
                                None,
                                "https://em-content.zobj.net/source/twitter/53/robot-face_1f916.png"
                            ),
                            type="messages"
                        ),
                        textbox=gr.Textbox(
                            placeholder="💬 请询问关于三三制查经方法的问题...",
                            container=False,
                            scale=7,
                            show_label=False
                        ),
                        title="💬 查经助手对话",
                        description="三三制查经方法论的智能助手，支持实时交互和深度问答",
                        examples=[
                            "🔍 什么是三三制查经方法？",
                            "👁️ 如何在查经中应用观察步骤？",
                            "📚 请解释查经的三个方面",
                            "👥 小组查经的实际步骤是什么？",
                            "💡 查经时如何提出好的问题？",
                            "🎯 如何在生活中应用查经所学？"
                        ],
                        cache_examples=True
                    )

            with gr.Column(scale=1):
                with gr.Column(elem_classes=["control-panel"]):
                    gr.HTML('<div class="model-info"><h3>⚙️ 系统配置</h3></div>')

                    # Model selection
                    model_dropdown = gr.Dropdown(
                        choices=chatbot.get_available_models(),
                        value=chatbot.current_model,
                        label="🤖 AI模型选择",
                        info="选择用于回应的AI模型",
                        interactive=True
                    )

                    # Streaming toggle
                    streaming_checkbox = gr.Checkbox(
                        value=chatbot.streaming_enabled,
                        label="⚡ 启用流式输出",
                        info="实时流式显示回应内容"
                    )

                    # Status display
                    status_text = gr.Textbox(
                        value=(
                            f"✅ 就绪 | 模型: {chatbot.current_model} | "
                            f"流式: {'开启' if chatbot.streaming_enabled else '关闭'}"
                        ),
                        label="📊 系统状态",
                        interactive=False
                    )

                    # Info section
                    gr.HTML("""
                    <div class="feature-list">
                        <h4>🎯 可用功能:</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li>💬 智能查经问答</li>
                            <li>📖 课程内容解释</li>
                            <li>🎯 实践应用指导</li>
                            <li>🔧 多模型切换</li>
                            <li>📚 文档智能检索</li>
                            <li>👥 小组查经指导</li>
                        </ul>
                    </div>
                    """)

                    # Model info
                    gr.HTML(f"""
                    <div class="config-info">
                        <h4>🔧 当前配置:</h4>
                        <p><strong>🌡️ 温度:</strong> {settings.temperature}</p>
                        <p><strong>📏 最大令牌:</strong> {settings.max_tokens}</p>
                        <p><strong>🤖 代理类型:</strong> 查经 MCP</p>
                        <p><strong>🔌 工具支持:</strong> ✅ 已启用</p>
                        <p><strong>📝 语言:</strong> 中文优化</p>
                    </div>
                    """)

        # Event handlers
        def update_model(new_model):
            result = chatbot.switch_model(new_model)
            status = (
                f"🤖 模型: {chatbot.current_model} | "
                f"⚡ 流式: {'开启' if chatbot.streaming_enabled else '关闭'} | {result}"
            )
            return status

        def update_streaming(streaming):
            result = chatbot.toggle_streaming(streaming)
            status = (
                f"🤖 模型: {chatbot.current_model} | "
                f"⚡ 流式: {'开启' if chatbot.streaming_enabled else '关闭'} | {result}"
            )
            return status

        model_dropdown.change(
            update_model, inputs=[model_dropdown], outputs=[status_text]
        )
        streaming_checkbox.change(
            update_streaming, inputs=[streaming_checkbox], outputs=[status_text]
        )

    return demo


def main():
    """Main entry point."""
    import argparse

    parser = argparse.ArgumentParser(
        description="使用三三制聊天代理和MCP工具的Gradio聊天机器人"
    )
    parser.add_argument(
        "--host", default="127.0.0.1", help="绑定服务器的主机地址"
    )
    parser.add_argument(
        "--port", type=int, default=7860, help="绑定服务器的端口"
    )
    parser.add_argument(
        "--share", action="store_true", help="创建公共可分享链接"
    )
    parser.add_argument(
        "--debug", action="store_true", help="启用调试模式"
    )

    args = parser.parse_args()

    # Create and launch the interface
    demo = create_gradio_interface()

    print("🚀 正在启动三三制聊天Gradio界面...")
    print(f"📍 服务器将在以下地址可用: http://{args.host}:{args.port}")
    if args.share:
        print("🌐 已启用公共分享")

    demo.launch(
        server_name=args.host,
        server_port=args.port,
        share=args.share,
        debug=args.debug,
        show_error=True,
        quiet=False
    )


if __name__ == "__main__":
    main()
