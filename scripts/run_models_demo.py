#!/usr/bin/env python
"""
Demo script to test all available models in the Triple3 Chat API.
This will test both shorthand model names and raw model IDs.

# Using environment variables
export API_KEYS='["your-api-key"]'
poetry run python run_models_demo.py

# Using .env file
# Add API_KEYS='["your-api-key"]' to .env file
poetry run python run_models_demo.py

# Using command line (preferred for testing)
poetry run python run_models_demo.py --api-key "your-api-key"
"""

import argparse
import json
import os
import sys
import time
from pathlib import Path
from typing import Any

import requests
from dotenv import load_dotenv
from requests.exceptions import RequestException, Timeout

# ANSI color codes for terminal output
COLORS = {
    "GREEN": "\033[92m",
    "YELLOW": "\033[93m",
    "RED": "\033[91m",
    "BLUE": "\033[94m",
    "BOLD": "\033[1m",
    "UNDERLINE": "\033[4m",
    "END": "\033[0m",
}

# Load environment variables from .env file in project root directory
env_path = Path(__file__).parent.parent / ".env"
load_dotenv(dotenv_path=env_path)
if os.path.exists(env_path):
    print(f"{COLORS['GREEN']}Loaded environment from {env_path}{COLORS['END']}")
else:
    print(f"{COLORS['RED']}Warning: No .env file found at {env_path}{COLORS['END']}")
    print("You may need to provide an API key with the --api-key option")

# Configuration
API_BASE_URL = "http://127.0.0.1:8000/api/v1"


# Read API_KEYS from environment
def get_api_key(cmd_line_key=None):
    """Get API key from command line, environment variables or .env file.

    Args:
        cmd_line_key: API key provided via command line (highest priority)

    Returns:
        API key to use for authentication
    """
    # Command line has highest priority
    if cmd_line_key:
        return cmd_line_key

    # Next try environment variable API_KEYS as JSON array (project standard)
    api_keys_str = os.environ.get("API_KEYS", "[]")
    try:
        api_keys = json.loads(api_keys_str)
        if isinstance(api_keys, list) and len(api_keys) > 0:
            return api_keys[0]
    except json.JSONDecodeError:
        pass

    # Fallback to direct API_KEY environment variable
    return os.environ.get("API_KEY", "")


# We'll set this later after parsing command line arguments
API_KEY = None
HEADERS = {"Content-Type": "application/json"}


# These functions will be called after API_KEY is set
def setup_auth_headers(api_key):
    """Set up authentication headers with the provided API key.

    Args:
        api_key: API key to use for authentication
    """
    global HEADERS
    if api_key:
        HEADERS["Authorization"] = f"Bearer {api_key}"
    elif "Authorization" in HEADERS:
        del HEADERS["Authorization"]


def check_api_key(api_key):
    """Check if API key is available and warn if not.

    Args:
        api_key: API key to check
    """
    if not api_key:
        print(f"{COLORS['YELLOW']}⚠️  Warning: No API key provided.{COLORS['END']}")
        print("   You can provide an API key using the --api-key option")
        print("   or by setting the API_KEYS environment variable.")
        print("   Testing may fail if authentication is required.")


# Default timeout in seconds
DEFAULT_TIMEOUT = 30


def get_available_models(timeout: int = DEFAULT_TIMEOUT) -> dict[str, dict[str, Any]]:
    """Get list of available models from the API.

    Args:
        timeout: Request timeout in seconds

    Returns:
        Dictionary of available models with their details
    """
    global API_BASE_URL
    print(
        f"{COLORS['BLUE']}Fetching available models from "
        f"{API_BASE_URL}...{COLORS['END']}"
    )
    try:
        response = requests.get(
            f"{API_BASE_URL}/models", headers=HEADERS, timeout=timeout
        )
        response.raise_for_status()
        models = response.json()["models"]
        print(
            f"{COLORS['GREEN']}Successfully retrieved {len(models)} "
            f"models.{COLORS['END']}"
        )
        return models
    except Timeout:
        print(
            f"{COLORS['RED']}Error: Request timed out after {timeout} "
            f"seconds{COLORS['END']}"
        )
        return {}
    except RequestException as e:
        print(f"{COLORS['RED']}Error fetching models: {e}{COLORS['END']}")
        if hasattr(e, "response") and e.response is not None:
            print(
                f"{COLORS['RED']}Response: {e.response.status_code} - "
                f"{e.response.text}{COLORS['END']}"
            )
        return {}


def test_chat_completion(
    model: str,
    messages: list[dict[str, str]],
    max_tokens: int | None = 100,
    temperature: float = 0.7,
    timeout: int = DEFAULT_TIMEOUT,
    verbose: bool = True,
) -> dict[str, Any]:
    """Test a chat completion request with the specified model.

    Args:
        model: Model name or ID to test
        messages: List of messages for the conversation
        max_tokens: Maximum number of tokens to generate
        temperature: Temperature for text generation (0.0-1.0)
        timeout: Request timeout in seconds
        verbose: Whether to print detailed output

    Returns:
        Response data from the API or error info
    """
    data = {
        "model": model,
        "messages": messages,
        "max_tokens": max_tokens,
        "temperature": temperature,
    }

    print(f"{COLORS['BOLD']}Testing model: {COLORS['BLUE']}{model}{COLORS['END']}")
    if verbose:
        print(f"Request data: {json.dumps(data, ensure_ascii=False)}")

    start_time = (
        time.time()
    )  # Move this outside the try block to ensure it's always defined

    global API_BASE_URL
    try:
        response = requests.post(
            f"{API_BASE_URL}/chat/completions",
            headers=HEADERS,
            json=data,
            timeout=timeout,
        )
        end_time = time.time()

        duration = end_time - start_time
        response.raise_for_status()

        response_data = response.json()
        print(
            f"{COLORS['GREEN']}✓ Success! Response received in "
            f"{duration:.2f} seconds{COLORS['END']}"
        )

        content = response_data["choices"][0]["message"]["content"]
        truncated_content = content[:100] + ("..." if len(content) > 100 else "")

        if verbose:
            print(f"Response content: {truncated_content}")

        # Calculate token rate
        total_tokens = response_data["usage"]["total_tokens"]
        tokens_per_second = total_tokens / duration if duration > 0 else 0

        print(
            f"Token usage: {response_data['usage']} "
            f"({tokens_per_second:.1f} tokens/sec)"
        )
        print("-" * 80)
        return response_data

    except Timeout:
        duration = time.time() - start_time
        print(
            f"{COLORS['RED']}✗ Error: Request timed out after {timeout} "
            f"seconds ({duration:.2f}s elapsed){COLORS['END']}"
        )
        print("-" * 80)
        return {"error": "timeout", "model": model, "duration": duration}

    except RequestException as e:
        duration = time.time() - start_time
        print(
            f"{COLORS['RED']}✗ Error! Failed after {duration:.2f} seconds: "
            f"{str(e)}{COLORS['END']}"
        )

        if hasattr(e, "response") and e.response is not None:
            print(f"Status code: {e.response.status_code}")
            print(f"Response: {e.response.text}")

        print("-" * 80)
        return {"error": str(e), "model": model}


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Test the Triple3 Chat API with various models."
    )

    # General parameters
    parser.add_argument(
        "-m", "--model", help="Test only a specific model (shorthand or raw ID)"
    )
    parser.add_argument(
        "-k",
        "--api-key",
        help="API key for authentication (overrides environment variable)",
    )
    parser.add_argument(
        "--base-url",
        default=API_BASE_URL,
        help=f"Base URL for the API (default: {API_BASE_URL})",
    )
    parser.add_argument(
        "-t",
        "--timeout",
        type=int,
        default=DEFAULT_TIMEOUT,
        help=f"Request timeout in seconds (default: {DEFAULT_TIMEOUT})",
    )
    parser.add_argument(
        "--max-tokens",
        type=int,
        default=100,
        help="Maximum tokens to generate (default: 100)",
    )
    parser.add_argument(
        "--temperature",
        type=float,
        default=0.7,
        help="Temperature for text generation (default: 0.7)",
    )
    parser.add_argument(
        "--message",
        default="请简短介绍一下你自己。说明一下你是什么大型语言模型。",
        help="Test message to send to the API",
    )

    # Test mode options
    parser.add_argument(
        "--raw-only", action="store_true", help="Test only raw model IDs"
    )
    parser.add_argument(
        "--shorthand-only", action="store_true", help="Test only shorthand model names"
    )
    parser.add_argument(
        "--include-custom",
        action="store_true",
        help="Include testing custom model IDs not in config",
    )
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="Show verbose output"
    )

    return parser.parse_args()


def main():
    """Main entry point."""
    # Parse command line arguments
    args = parse_args()

    # Set up API key and base URL
    global API_BASE_URL, API_KEY
    API_BASE_URL = args.base_url
    API_KEY = get_api_key(args.api_key)

    # Set up authentication headers
    setup_auth_headers(API_KEY)

    # Check if API key is available and warn if not
    check_api_key(API_KEY)

    # Get available models
    models = get_available_models(timeout=args.timeout)
    if not models and not args.model:
        print(
            f"{COLORS['RED']}No models available and no specific model "
            f"specified. Exiting.{COLORS['END']}"
        )
        return 1

    # If we have models, show a summary
    if models:
        print(f"{COLORS['BOLD']}Found {len(models)} available models{COLORS['END']}")
        if args.verbose:
            for name, details in models.items():
                print(f"  - {name} ({details['id']})")

    if args.verbose and API_KEY:
        print(
            f"{COLORS['GREEN']}Using API key: {API_KEY[:5]}..."
            f"{API_KEY[-4:]}{COLORS['END']}"
        )
    elif args.verbose:
        print(f"{COLORS['YELLOW']}No API key provided{COLORS['END']}")

    # Prepare test message
    test_messages = [{"role": "user", "content": args.message}]

    # If a specific model was requested, test only that one
    if args.model:
        print(
            f"\n{COLORS['BOLD']}=== TESTING REQUESTED MODEL: "
            f"{args.model} ==={COLORS['END']}\n"
        )
        test_chat_completion(
            model=args.model,
            messages=test_messages,
            max_tokens=args.max_tokens,
            temperature=args.temperature,
            timeout=args.timeout,
            verbose=args.verbose,
        )
        return 0

    # Otherwise, test models according to the requested mode
    results = {}

    # Test shorthand model names
    if not args.raw_only:
        print(
            f"\n{COLORS['BOLD']}=== TESTING SHORTHAND MODEL NAMES ==={COLORS['END']}\n"
        )
        for shorthand, details in models.items():
            result = test_chat_completion(
                model=shorthand,
                messages=test_messages,
                max_tokens=args.max_tokens,
                temperature=args.temperature,
                timeout=args.timeout,
                verbose=args.verbose,
            )
            if "error" not in result:
                results[f"shorthand:{shorthand}"] = True
            else:
                results[f"shorthand:{shorthand}"] = False

    # Test raw model IDs
    if not args.shorthand_only:
        print(f"\n{COLORS['BOLD']}=== TESTING RAW MODEL IDS ==={COLORS['END']}\n")
        for shorthand, details in models.items():
            result = test_chat_completion(
                model=details["id"],
                messages=test_messages,
                max_tokens=args.max_tokens,
                temperature=args.temperature,
                timeout=args.timeout,
                verbose=args.verbose,
            )
            if "error" not in result:
                results[f"raw:{details['id']}"] = True
            else:
                results[f"raw:{details['id']}"] = False

    # Test a custom raw model ID not in our config
    if args.include_custom and not args.shorthand_only:
        print(f"\n{COLORS['BOLD']}=== TESTING CUSTOM RAW MODEL ID ==={COLORS['END']}\n")
        custom_model_id = "openai/gpt-3.5-turbo"  # A model not in our config
        result = test_chat_completion(
            model=custom_model_id,
            messages=test_messages,
            max_tokens=args.max_tokens,
            temperature=args.temperature,
            timeout=args.timeout,
            verbose=args.verbose,
        )
        if "error" not in result:
            results[f"custom:{custom_model_id}"] = True
        else:
            results[f"custom:{custom_model_id}"] = False

    # Print summary
    success_count = sum(1 for v in results.values() if v)
    total_count = len(results)

    print(f"\n{COLORS['BOLD']}=== TEST SUMMARY ==={COLORS['END']}")
    print(
        f"Successful tests: {success_count}/{total_count} "
        f"({success_count/total_count*100:.1f}%)"
    )

    return 0


if __name__ == "__main__":
    sys.exit(main())
