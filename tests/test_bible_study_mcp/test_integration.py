"""
Integration tests for the MCP server functionality.
"""

import asyncio
import sys
from unittest.mock import AsyncMock, Mock, patch

import pytest

# Import to ensure module is loaded, then get actual module from sys.modules
from src.bible_study_mcp.context import ContextLoader
from src.bible_study_mcp.server import get_server

mcp_module = sys.modules["src.bible_study_mcp.server"]


class TestMCPIntegration:
    """Integration test cases for the complete MCP server."""

    @pytest.fixture
    def mock_documents_dir(self, tmp_path):
        """Create a temporary documents directory with mock files."""
        docs_dir = tmp_path / "documents"
        docs_dir.mkdir()

        # Create mock overview file
        overview_file = docs_dir / "0 三三制总结.md"
        overview_file.write_text("# 三三制查经方法\n\n这是测试内容。", encoding="utf-8")

        # Create mock class files
        for i in range(1, 4):
            class_file = docs_dir / f"{i} 第18期三三制第{'一二三'[i-1]}讲文字版.docx"
            class_file.touch()  # Create empty file

        return docs_dir

    @pytest.fixture
    def mock_context_loader(self, mock_documents_dir):
        """Create a ContextLoader with mocked document loading."""
        with patch("src.bible_study_mcp.context.Document") as mock_doc_class:
            # Mock docx Document
            mock_doc = Mock()
            mock_paragraph1 = Mock()
            mock_paragraph1.text = "测试课程内容第1段"
            mock_paragraph2 = Mock()
            mock_paragraph2.text = "测试课程内容第2段"
            mock_doc.paragraphs = [mock_paragraph1, mock_paragraph2]
            mock_doc_class.return_value = mock_doc

            loader = ContextLoader(mock_documents_dir)
            return loader

    # Note: Basic server initialization and handler tests are covered by unit tests

    @pytest.mark.asyncio
    async def test_encoding_handling(self, mock_context_loader):
        """Test proper handling of different text encodings."""
        from src.bible_study_mcp.server import method_overview

        with patch.object(mcp_module, "context_loader", mock_context_loader):
            # Test with Chinese characters
            mock_context_loader.load_method_overview = AsyncMock(
                return_value="# 三三制查经方法\n\n中文内容测试"
            )

            result = await method_overview()
            assert "三三制查经方法" in result
            assert "中文内容测试" in result

    @pytest.mark.asyncio
    async def test_cancellation_integration(self):
        """Test cancellation support in the integration."""
        from src.bible_study_mcp.server import method_overview

        with patch.object(mcp_module, "context_loader") as mock_loader:
            # Test that operations can be cancelled
            mock_loader.load_method_overview = AsyncMock(
                side_effect=asyncio.CancelledError()
            )

            with pytest.raises(asyncio.CancelledError):
                await method_overview()

    @pytest.mark.asyncio
    async def test_environment_variable_integration(self):
        """Test environment variable integration."""
        import os

        # Test that the server works with environment variables
        with patch.dict(os.environ, {"DOCUMENTS_DIR": "/tmp/test"}):
            server = get_server()
            assert server is not None
