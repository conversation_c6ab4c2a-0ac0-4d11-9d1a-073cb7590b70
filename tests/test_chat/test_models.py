"""
Tests for the chat.models module.
"""

import pytest
from pydantic import ValidationError

from chat.models import (
    ChatCompletionChoice,
    ChatCompletionRequest,
    ChatCompletionResponse,
    ChatCompletionStreamChoice,
    ChatCompletionStreamResponse,
    Delta,
    ErrorResponse,
    HealthResponse,
    Message,
    Role,
    Usage,
)


class TestMessage:
    """Test Message model."""

    def test_message_creation(self):
        """Test creating a valid message."""
        message = Message(role=Role.USER, content="Hello")
        assert message.role == Role.USER
        assert message.content == "Hello"
        assert message.name is None

    def test_message_with_name(self):
        """Test creating a message with name."""
        message = Message(role=Role.USER, content="Hello", name="test_user")
        assert message.name == "test_user"

    def test_invalid_role(self):
        """Test that invalid roles raise validation error."""
        with pytest.raises(ValidationError):
            # Use a literal that's not a valid Role enum value
            Message(role="invalid_role", content="Hello")  # type: ignore


class TestRole:
    """Test Role enum."""

    def test_all_roles(self):
        """Test all valid roles."""
        assert Role.SYSTEM == "system"
        assert Role.USER == "user"
        assert Role.ASSISTANT == "assistant"
        assert Role.FUNCTION == "function"


class TestChatCompletionRequest:
    """Test ChatCompletionRequest model."""

    def test_minimal_request(self):
        """Test creating a minimal request."""
        messages = [Message(role=Role.USER, content="Hello")]
        request = ChatCompletionRequest(model="test-model", messages=messages)
        
        assert request.model == "test-model"
        assert len(request.messages) == 1
        assert request.max_tokens is None
        assert request.temperature == 0.7
        assert request.stream is False

    def test_full_request(self):
        """Test creating a full request with all parameters."""
        messages = [
            Message(role=Role.SYSTEM, content="You are a helpful assistant"),
            Message(role=Role.USER, content="Hello")
        ]
        request = ChatCompletionRequest(
            model="test-model",
            messages=messages,
            max_tokens=100,
            temperature=0.5,
            stream=True
        )
        
        assert request.model == "test-model"
        assert len(request.messages) == 2
        assert request.max_tokens == 100
        assert request.temperature == 0.5
        assert request.stream is True

    def test_temperature_values(self):
        """Test that temperature values are accepted."""
        messages = [Message(role=Role.USER, content="Hello")]
        
        # Test valid temperature values
        request = ChatCompletionRequest(
            model="test-model",
            messages=messages,
            temperature=0.5
        )
        assert request.temperature == 0.5
        
        # Test edge cases (negative temperature is allowed in this implementation)
        request_negative = ChatCompletionRequest(
            model="test-model",
            messages=messages,
            temperature=-1.0
        )
        assert request_negative.temperature == -1.0


class TestChatCompletionResponse:
    """Test ChatCompletionResponse model."""

    def test_response_creation(self):
        """Test creating a valid response."""
        message = Message(role=Role.ASSISTANT, content="Hello there!")
        choice = ChatCompletionChoice(index=0, message=message, finish_reason="stop")
        usage = Usage(prompt_tokens=10, completion_tokens=5, total_tokens=15)
        
        response = ChatCompletionResponse(
            id="test-123",
            model="test-model",
            choices=[choice],
            usage=usage
        )
        
        assert response.id == "test-123"
        assert response.object == "chat.completion"
        assert response.model == "test-model"
        assert len(response.choices) == 1
        assert response.usage.total_tokens == 15


class TestDelta:
    """Test Delta model for streaming responses."""

    def test_delta_with_role(self):
        """Test creating a delta with role."""
        delta = Delta(role=Role.ASSISTANT)
        assert delta.role == Role.ASSISTANT
        assert delta.content is None

    def test_delta_with_content(self):
        """Test creating a delta with content."""
        delta = Delta(content="Hello")
        assert delta.role is None
        assert delta.content == "Hello"

    def test_empty_delta(self):
        """Test creating an empty delta."""
        delta = Delta()
        assert delta.role is None
        assert delta.content is None


class TestStreamChoice:
    """Test StreamChoice model."""

    def test_stream_choice_creation(self):
        """Test creating a stream choice."""
        delta = Delta(content="Hello")
        choice = ChatCompletionStreamChoice(index=0, delta=delta, finish_reason=None)
        
        assert choice.index == 0
        assert choice.delta.content == "Hello"
        assert choice.finish_reason is None

    def test_stream_choice_with_finish_reason(self):
        """Test creating a stream choice with finish reason."""
        delta = Delta()
        choice = ChatCompletionStreamChoice(index=0, delta=delta, finish_reason="stop")
        
        assert choice.finish_reason == "stop"


class TestChatCompletionStreamResponse:
    """Test ChatCompletionStreamResponse model."""

    def test_stream_response_creation(self):
        """Test creating a stream response."""
        delta = Delta(content="Hello")
        choice = ChatCompletionStreamChoice(index=0, delta=delta, finish_reason=None)
        
        response = ChatCompletionStreamResponse(
            id="test-123",
            model="test-model",
            choices=[choice]
        )
        
        assert response.id == "test-123"
        assert response.object == "chat.completion.chunk"
        assert response.model == "test-model"
        assert len(response.choices) == 1


class TestUsage:
    """Test Usage model."""

    def test_usage_creation(self):
        """Test creating usage information."""
        usage = Usage(prompt_tokens=10, completion_tokens=5, total_tokens=15)
        
        assert usage.prompt_tokens == 10
        assert usage.completion_tokens == 5
        assert usage.total_tokens == 15


class TestHealthResponse:
    """Test HealthResponse model."""

    def test_health_response_creation(self):
        """Test creating a health response."""
        response = HealthResponse(
            status="healthy",
            version="1.0.0"
        )
        
        assert response.status == "healthy"
        assert response.version == "1.0.0"

    def test_health_response_default_status(self):
        """Test health response with default status."""
        response = HealthResponse(version="1.0.0")
        assert response.status == "ok"


class TestErrorResponse:
    """Test ErrorResponse model."""

    def test_error_response_creation(self):
        """Test creating an error response."""
        error_response = ErrorResponse(
            error={
                "message": "Test error",
                "type": "test_error",
                "code": "TEST001"
            }
        )
        
        assert error_response.error["message"] == "Test error"
        assert error_response.error["type"] == "test_error"
        assert error_response.error["code"] == "TEST001"
