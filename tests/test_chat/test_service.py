"""
Tests for the chatbot service.

Note: Most service functionality is better tested through integration tests
in test_api.py and test_streaming.py. This file focuses on service-specific
logic that can't be easily tested through the API layer.
"""

# Currently no meaningful service-specific tests that aren't better covered
# by integration tests. Service tests that mock the entire service method
# don't provide value as they don't test real functionality.
