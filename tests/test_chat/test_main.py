"""
Tests for the chat.main module.
"""

import pytest
from fastapi.testclient import TestClient

from chat.main import app


class TestMainApp:
    """Test the main FastAPI application."""

    @pytest.fixture
    def client(self):
        """Create test client for the main app."""
        return TestClient(app)

    def test_app_creation(self):
        """Test that the app is created successfully."""
        assert app is not None
        assert app.title == "RHCC Chatbot API"

    def test_cors_middleware(self, client):
        """Test CORS middleware is configured."""
        # Test CORS headers on a GET request instead of OPTIONS
        response = client.get("/api/v1/health", headers={"Origin": "http://localhost:3000"})
        assert response.status_code == 200
        # CORS headers should be present (added by middleware)
        assert "access-control-allow-origin" in [h.lower() for h in response.headers.keys()]

    def test_health_endpoint_accessible(self, client):
        """Test that health endpoint is accessible through main app."""
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ok"
        assert data["version"] == "v1"

    def test_models_endpoint_accessible(self, client):
        """Test that models endpoint is accessible through main app."""
        # Note: This may require authentication depending on config
        response = client.get("/api/v1/models")
        # Should either return 200 (success) or 401 (auth required)
        assert response.status_code in [200, 401]

    def test_chat_completions_endpoint_exists(self, client):
        """Test that chat completions endpoint exists."""
        # Test with invalid data to ensure endpoint exists
        response = client.post("/api/v1/chat/completions", json={})
        # Should return 422 (validation error) or 401 (auth required), not 404
        assert response.status_code in [401, 422]



    def test_docs_endpoint(self, client):
        """Test Swagger docs endpoint."""
        response = client.get("/api/v1/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
