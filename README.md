# Triple3 Chat

A multi-LLM chat API service with LangGraph agent integration and MCP server for Bible study methodology.

## Features

- **Multi-LLM Support**: OpenAI, Anthropic, DeepSeek via OpenRouter
- **LangGraph Agent**: Intelligent agent with MCP tool integration
- **MCP Server**: Model Context Protocol server for Bible study methodology
- **Streaming Support**: Real-time response streaming
- **Multiple Interfaces**: REST API, Terminal CLI, and Gradio web interface
- **Chinese Language Support**: Specialized support for Chinese Bible study materials

## Quick Start

### Install Dependencies
```bash
poetry install
```

### Run Services

#### Chat API Service (REST API)
```bash
# Run on default port 8000
poetry run python -m src.chat.main

# Run on custom port with uvicorn
poetry run uvicorn src.chat.main:app --host 0.0.0.0 --port 8080 --reload
```

#### Terminal Chatbot (Agent Mode)
```bash
# Interactive terminal chatbot with MCP agent
poetry run python scripts/terminal_chatbot.py

# List available models
poetry run python scripts/terminal_chatbot.py --list-models

# Use specific model
poetry run python scripts/terminal_chatbot.py --model claude-3.5-sonnet
```

#### Gradio Web Interface
```bash
# Web-based chatbot interface
poetry run python scripts/gradio_chatbot.py
```

#### MCP Server
```bash
# Run MCP server for Bible study context
poetry run python scripts/run_mcp_server.py

# Using shell script (recommended for MCP clients)
./scripts/run_mcp_server.sh
```

### Chinese Language Demo
```bash
# Test Chinese language with MCP agent
poetry run python scripts/demo_chinese_agent.py
```

## Project Structure

```
triple3-chat/
├── src/
│   ├── chat/              # REST API service
│   │   ├── main.py        # FastAPI application
│   │   ├── api.py         # API endpoints
│   │   ├── service.py     # Chat service logic
│   │   ├── agent_service.py # LangGraph agent integration
│   │   └── llm.py         # Multi-LLM support
│   └── bible_study_mcp/   # MCP server implementation
│       ├── server.py      # MCP server with prompts/tools
│       └── context.py     # Document processing
├── scripts/               # Command-line interfaces
│   ├── terminal_chatbot.py # Interactive terminal chat
│   ├── gradio_chatbot.py   # Web interface
│   └── run_mcp_server.py   # MCP server runner
├── documents/             # 三三制 Bible study materials
├── tests/                 # Comprehensive test suite
└── CLAUDE.md             # Detailed development guide
```

## Development

### Testing
```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=src

# Test specific modules
poetry run pytest tests/test_chat/ -v
poetry run pytest tests/test_bible_study_mcp/ -v
```

### Code Quality
```bash
# Format and lint with ruff
poetry run ruff format .
poetry run ruff check .

# Auto-fix issues
poetry run ruff check --fix .
```

### API Testing
```bash
# Test deployed API
./test_api.sh

# Test with authentication
./test_api.sh your-api-key
```

## Configuration

Create a `.env` file with:
```bash
OPENROUTER_API_KEY=your_openrouter_key
API_KEYS=your_client_api_key1,your_client_api_key2
DEBUG=true
```

## Deployment

Deploy to Google Cloud Run:
```bash
./deploy.sh
```

For detailed documentation, see [CLAUDE.md](./CLAUDE.md).
