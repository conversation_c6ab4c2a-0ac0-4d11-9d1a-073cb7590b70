# API configuration
API_TITLE="RHCC Chatbot API"
API_DESCRIPTION="API for chatting with RHCC's LLM service"
API_VERSION="v1"
API_PREFIX="/api/v1"
DEBUG=false

# Authentication
# API keys are now provided via HTTP Bearer authentication
# Example: Authorization: Bearer your-api-key
API_KEYS=["your-api-key-1", "your-api-key-2"]  # List of valid API keys

# OpenRouter configuration
OPENROUTER_API_KEY="your-api-key"
OPENROUTER_BASE_URL="https://openrouter.ai/api/v1"
DEFAULT_MODEL="gpt-4.1"

# LLM configuration
SYSTEM_MESSAGE="You are a helpful AI assistant."
MAX_TOKENS=4000
TEMPERATURE=0.7

# Rate limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60  # seconds

# Agent configuration (LangGraph integration)
USE_AGENT=false  # Set to true to enable Lang<PERSON><PERSON>h agent with Bible study tools
AGENT_MCP_SERVER_PATH=""  # Optional: Path to MCP server script (auto-detected if empty)

# Logging configuration
LOG_FILE=""  # Optional: Path to log file (logs to console only if empty)
LOG_LEVEL="INFO"  # Options: DEBUG, INFO, WARNING, ERROR
