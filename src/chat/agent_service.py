"""
LangGraph agent service implementation with proper MCP tools integration.

This module implements a LangGraph ReAct agent that connects to the Bible study
MCP server and uses its tools, prompts, and resources following MCP best practices.
"""

import logging
import sys
import uuid
from collections.abc import AsyncGenerator
from pathlib import Path

from langchain_core.runnables import RunnableConfig
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent

from chat.config import settings
from chat.llm import get_chat_model
from chat.models import (
    ChatCompletionChoice,
    ChatCompletionRequest,
    ChatCompletionResponse,
    ChatCompletionStreamChoice,
    ChatCompletionStreamResponse,
    Delta,
    Message,
    Role,
    Usage,
)

logger = logging.getLogger(__name__)


class AgentService:
    """Service for handling LangGraph agent interactions with MCP tools."""

    def __init__(self):
        self._agent = None
        self._checkpointer = MemorySaver()
        self._mcp_client = None
        self._mcp_session = None
        self._tools = None
        self._system_prompt = None

    def _get_mcp_server_path(self) -> str:
        """Get the path to the MCP server script."""
        if (
            settings.agent_mcp_server_path
            and Path(settings.agent_mcp_server_path).exists()
        ):
            return settings.agent_mcp_server_path

        # Auto-detect MCP server path
        project_root = Path(__file__).parent.parent.parent
        mcp_server_path = project_root / "src" / "bible_study_mcp" / "server.py"

        if not mcp_server_path.exists():
            raise FileNotFoundError(f"MCP server not found at {mcp_server_path}")

        return str(mcp_server_path)

    def _initialize_mcp_client(self):
        """Initialize MCP client connection to Bible study server."""
        if self._mcp_client is not None:
            return self._mcp_client

        try:
            # Get path to MCP server
            server_path = self._get_mcp_server_path()

            # Use MultiServerMCPClient with stdio transport
            self._mcp_client = MultiServerMCPClient(
                {
                    "bible-study": {
                        "command": sys.executable,
                        "args": [server_path],
                        "transport": "stdio",
                    }
                }
            )

            logger.info(
                f"Initialized MCP client for Bible study server at {server_path}"
            )
            return self._mcp_client

        except Exception as e:
            logger.error(f"Failed to initialize MCP client: {e}")
            raise

    async def _load_mcp_tools(self):
        """Load tools from the MCP server with session caching."""
        if self._tools is not None:
            return self._tools

        try:
            client = self._initialize_mcp_client()

            # Create tools with a fresh session each time, but cache the tools
            async with client.session("bible-study") as session:
                # Load tools from the MCP server
                self._tools = await load_mcp_tools(session)
                logger.info(f"Loaded {len(self._tools)} tools from MCP server")

                # Store the client for later use by tools
                self._mcp_client = client
                return self._tools

        except Exception as e:
            logger.error(f"Failed to load MCP tools: {e}")
            # Return empty list as fallback
            return []

    async def _get_mcp_system_prompt(self) -> str:
        """Get the system prompt from MCP server."""
        if self._system_prompt is not None:
            return self._system_prompt

        try:
            client = self._initialize_mcp_client()

            async with client.session("bible-study") as session:
                # List available prompts
                prompts_result = await session.list_prompts()

                # Look for the bible_study_method prompt
                for prompt_info in prompts_result.prompts:
                    if prompt_info.name == "bible_study_method":
                        # Get the prompt content
                        prompt_result = await session.get_prompt(
                            name="bible_study_method", arguments={}
                        )

                        if prompt_result.messages:
                            # Extract content from the first message, handling different
                            # content types
                            first_message = prompt_result.messages[0]
                            if hasattr(first_message, "content"):
                                content = first_message.content
                                # Handle different content types according to MCP
                                # specification
                                # Try to get text content safely
                                text_content = getattr(content, "text", None)
                                if text_content:
                                    # TextContent type
                                    self._system_prompt = str(text_content)
                                elif isinstance(content, str):
                                    # Direct string content
                                    self._system_prompt = content
                                else:
                                    # For other content types, try to extract as string
                                    try:
                                        # Try to access mimeType and data safely
                                        mime_type = getattr(content, "mimeType", None)
                                        data = getattr(content, "data", None)

                                        if mime_type and data:
                                            if mime_type.startswith("text/"):
                                                self._system_prompt = str(data)
                                            else:
                                                logger.warning(
                                                    f"Unsupported content type: "
                                                    f"{mime_type}"
                                                )
                                                continue
                                        else:
                                            # Fallback to string representation
                                            self._system_prompt = str(content)
                                    except Exception as e:
                                        logger.warning(
                                            f"Error extracting content: {e}, "
                                            f"using string representation"
                                        )
                                        self._system_prompt = str(content)

                                if self._system_prompt:
                                    logger.info(
                                        "Successfully loaded system prompt from "
                                        "MCP server"
                                    )
                                    return self._system_prompt

                # If we couldn't find the prompt, log a warning
                logger.warning("Could not find bible_study_method prompt in MCP server")

        except Exception as e:
            logger.warning(f"Failed to get system prompt from MCP server: {e}")

        # Fallback to settings or default
        if settings.system_message:
            logger.info("Using system message from settings as fallback")
            return settings.system_message
        else:
            logger.info("Using default Bible study system prompt as fallback")
            return (
                "You are a Bible study assistant specialized in the "
                "三三制 (Triple-Three Method) approach. Use the available tools "
                "to help users with their Bible study questions and provide "
                "guidance based on the method's principles."
            )

    async def _get_agent(
        self,
        model_name: str,
        temperature: float | None,
        max_tokens: int | None,
    ):
        """Get or create LangGraph agent with MCP tools."""
        try:
            # Get the chat model
            chat_model, _ = get_chat_model(
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )

            # Load tools from MCP server
            tools = await self._load_mcp_tools()

            # Get system prompt from MCP server
            system_prompt = await self._get_mcp_system_prompt()

            # Create the agent with MCP tools and prompt
            self._agent = create_react_agent(
                model=chat_model,
                tools=tools,
                prompt=system_prompt,
                checkpointer=self._checkpointer,
            )

            logger.info(f"Created LangGraph agent with {len(tools)} MCP tools")
            return self._agent

        except Exception as e:
            logger.error(f"Error creating agent: {e}")
            raise

    async def _cleanup(self):
        """Clean up MCP client resources."""
        try:
            # Clean up resources
            self._mcp_client = None
            self._mcp_session = None
            self._tools = None
            self._system_prompt = None
            logger.debug("Cleaned up MCP client resources")
        except Exception as e:
            logger.warning(f"Error during MCP client cleanup: {e}")

    @staticmethod
    async def generate_chat_completion(
        request: ChatCompletionRequest,
    ) -> ChatCompletionResponse:
        """Generate a chat completion response using the LangGraph agent with MCP
        tools.
        """
        agent_service = AgentService()

        try:
            # Initialize MCP client first
            client = agent_service._initialize_mcp_client()

            # Use a persistent session for the entire conversation
            async with client.session("bible-study") as session:
                # Load tools within the session context
                agent_service._tools = await load_mcp_tools(session)
                logger.info(f"Loaded {len(agent_service._tools)} tools from MCP server")

                # Store the client for later use
                agent_service._mcp_client = client

                # Get the agent with MCP tools
                agent = await agent_service._get_agent(
                    model_name=request.model,
                    temperature=request.temperature,
                    max_tokens=request.max_tokens,
                )

                # Convert messages to the format expected by LangGraph
                messages = [
                    {"role": msg.role.value, "content": msg.content}
                    for msg in request.messages
                ]

                # Generate a thread ID for this conversation
                thread_id = str(uuid.uuid4())
                config = RunnableConfig(configurable={"thread_id": thread_id})

                # Invoke the agent within the session context
                result = await agent.ainvoke({"messages": messages}, config=config)

                # Extract the final response
                if result and "messages" in result and result["messages"]:
                    last_message = result["messages"][-1]
                    if hasattr(last_message, "content"):
                        response_text = last_message.content
                    else:
                        response_text = str(last_message)
                else:
                    response_text = "I apologize, but I couldn't generate a response."

                choice = ChatCompletionChoice(
                    index=0,
                    message=Message(
                        role=Role.ASSISTANT,
                        content=response_text,
                    ),
                    finish_reason="stop",
                )

                # Note: Token usage tracking would require additional implementation
                # for LangGraph agents. For now, we'll use placeholder values.
                usage = Usage(
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0,
                )

                return ChatCompletionResponse(
                    id=f"chatcmpl-{uuid.uuid4()}",
                    model=request.model,
                    choices=[choice],
                    usage=usage,
                )

        except Exception as e:
            logger.exception(f"Error generating agent chat completion: {e}")
            raise
        finally:
            # Clean up MCP resources
            await agent_service._cleanup()

    @staticmethod
    async def stream_chat_completion(
        request: ChatCompletionRequest,
    ) -> AsyncGenerator[ChatCompletionStreamResponse, None]:
        """Generate a streaming chat completion response using the LangGraph agent
        with MCP tools.
        """
        agent_service = AgentService()

        try:
            # Initialize MCP client first
            client = agent_service._initialize_mcp_client()

            # Use a persistent session for the entire conversation
            async with client.session("bible-study") as session:
                # Load tools within the session context
                agent_service._tools = await load_mcp_tools(session)
                logger.info(f"Loaded {len(agent_service._tools)} tools from MCP server")

                # Store the client for later use
                agent_service._mcp_client = client

                # Get the agent with MCP tools
                agent = await agent_service._get_agent(
                    model_name=request.model,
                    temperature=request.temperature,
                    max_tokens=request.max_tokens,
                )

                # Convert messages to the format expected by LangGraph
                messages = [
                    {"role": msg.role.value, "content": msg.content}
                    for msg in request.messages
                ]

                # Generate unique IDs
                completion_id = f"chatcmpl-{uuid.uuid4()}"
                thread_id = str(uuid.uuid4())
                config = RunnableConfig(configurable={"thread_id": thread_id})

                # Send the initial chunk with role information
                initial_choice = ChatCompletionStreamChoice(
                    index=0,
                    delta=Delta(role=Role.ASSISTANT),
                    finish_reason=None,
                )

                initial_response = ChatCompletionStreamResponse(
                    id=completion_id,
                    model=request.model,
                    choices=[initial_choice],
                )

                yield initial_response

                # Stream the agent's execution
                accumulated_content = ""

                async for chunk in agent.astream({"messages": messages}, config=config):
                    # Extract content from the chunk
                    if isinstance(chunk, dict):
                        for node_name, node_data in chunk.items():
                            if node_name == "agent" and isinstance(node_data, dict):
                                messages_data = node_data.get("messages", [])
                                if messages_data:
                                    last_msg = messages_data[-1]
                                    if hasattr(last_msg, "content"):
                                        new_content = last_msg.content
                                        if (
                                            new_content
                                            and new_content != accumulated_content
                                        ):
                                            # Send the new content as a chunk
                                            content_chunk = new_content[
                                                len(accumulated_content) :
                                            ]
                                            if content_chunk:
                                                choice = ChatCompletionStreamChoice(
                                                    index=0,
                                                    delta=Delta(content=content_chunk),
                                                    finish_reason=None,
                                                )

                                                response = ChatCompletionStreamResponse(
                                                    id=completion_id,
                                                    model=request.model,
                                                    choices=[choice],
                                                )

                                                yield response
                                                accumulated_content = new_content

                # Send the final chunk with finish_reason
                final_choice = ChatCompletionStreamChoice(
                    index=0,
                    delta=Delta(),
                    finish_reason="stop",
                )

                final_response = ChatCompletionStreamResponse(
                    id=completion_id,
                    model=request.model,
                    choices=[final_choice],
                )

                yield final_response

        except Exception as e:
            logger.exception(f"Error generating streaming agent chat completion: {e}")
            raise
        finally:
            # Clean up MCP resources
            await agent_service._cleanup()
